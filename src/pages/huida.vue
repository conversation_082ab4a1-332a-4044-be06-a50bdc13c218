<template>
  <div class="huida-container theme-background">
    <div class="huida-page">
      <!-- 顶部导航栏 -->
      <Maintopbar
        :show-home-btn="true"
        :show-back-to-index-btn="true"
        :show-feature-intro="true"
        @home="handleHome"
        @back-to-index="handleBackToIndex"
      />

      <!-- 提醒内容容器 -->
      <div class="reminder-content-container">
        <PersonalMemo
          v-if="currentPersonId"
          ref="personalMemoRef"
          :user-id="currentUserId"
          :person-id="currentPersonId"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo" />
        <div v-else class="personal-memo-loading">
          <div class="memo-title">个人备忘录</div>
          <div class="memo-content">
            <div class="empty-memo">加载中...</div>
          </div>
        </div>
        <IndustryMemo @add-industry-memo="handleAddIndustryMemo" />
        <CompanyKnowledgeBase />
      </div>

      <!-- 中间聊天内容容器 -->
      <div class="huida-content-container">
        <div class="chat-content">
          <!-- 聊天内容区域 -->
          <div ref="scrollWrapper" class="chat-messages">
            <!-- 聊天消息列表 -->
            <template v-for="(item, index) in chatMessages" :key="item.key || index">
              <!-- 用户消息和AI回答使用chatItem组件 -->
              <ChatItem
                v-if="item.role === 'user' || item.role === 'assistant'"
                :message-data="item"
                :is-regenerate="false"
              />

              <!-- 思考过程组件 -->
              <div v-if="item.role === 'thinking'" class="thinking-process-container">
                <div class="thinking-process">
                  <!-- 思考过程标题 -->
                  <div class="thinking-header">
                    <div class="thinking-icon">🤔</div>
                    <div class="thinking-title">AI思考过程</div>
                  </div>

                  <!-- 思考内容列表 -->
                  <div class="thinking-content">
                    <div
                      v-for="(thinkingItem, thinkingIndex) in item.thinkingData.items"
                      :key="thinkingIndex"
                      class="thinking-item fade-in"
                    >
                      <div class="thinking-item-icon">
                        <div v-if="thinkingItem.type === 'status'" class="status-dot" :class="thinkingItem.step"></div>
                        <div v-else-if="thinkingItem.type === 'question'" class="question-icon">❓</div>
                        <div v-else-if="thinkingItem.type === 'log'" class="log-icon">📝</div>
                        <div v-else-if="thinkingItem.type === 'search_questions'" class="search-icon">🔍</div>
                        <div v-else-if="thinkingItem.type === 'search_result'" class="result-icon">📋</div>
                      </div>
                      <div class="thinking-item-content">
                        <div v-if="thinkingItem.type === 'status'" class="status-message">
                          {{ thinkingItem.message }}
                        </div>
                        <div v-else-if="thinkingItem.type === 'question'" class="question-content">
                          <span class="question-label">用户询问：</span>
                          <span class="question-text">{{ thinkingItem.question }}</span>
                        </div>
                        <div v-else-if="thinkingItem.type === 'log'" class="log-content">
                          {{ thinkingItem.message }}
                        </div>
                        <div v-else-if="thinkingItem.type === 'search_questions'" class="search-questions-content">
                          <div class="search-questions-label">AI分析如下角度搜索：</div>
                          <ul class="questions-list">
                            <li v-for="(question, qIndex) in thinkingItem.questions" :key="qIndex" class="question-item">
                              {{ question }}
                            </li>
                          </ul>
                        </div>
                        <div v-else-if="thinkingItem.type === 'search_result'" class="search-result-content">
                          <div class="search-result-title">搜索结果：</div>
                          <div v-for="(result, rIndex) in thinkingItem.results" :key="rIndex" class="result-item">
                            <a :href="result.link" target="_blank" class="result-link">
                              {{ result.title }}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 加载状态 -->
                  <div v-if="item.thinkingData.isLoading" class="loading-indicator">
                    <div class="loading-dots">
                      <span class="dot dot1">.</span>
                      <span class="dot dot2">.</span>
                      <span class="dot dot3">.</span>
                    </div>
                    <span class="loading-text">思考中...</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 底部输入区域 -->
      <div class="footer">
        <!-- 输入框 -->
        <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
          <inputBar
            ref="inputBarRef"
            @voice-send="handleInputSend"
            @send="handleInputSend"
            @stop="handleStop"
            @recording-status="handleRecordingStatus"
          />
        </form>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img src="@/assets/assistant/董会答.png" alt="董会答头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">董会答会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加个人备忘录弹窗 -->
    <AddPersonMemo
      v-if="showAddPersonMemo && currentPersonId"
      :user-id="currentUserId"
      :person-id="currentPersonId"
      @close="handleCloseAddPersonMemo"
      @save="handleSavePersonMemo"
    />

    <AddIndustryMemo
      v-if="showAddIndustryMemo"
      :user-id="currentUserId"
      @close="handleCloseAddIndustryMemo"
      @save="handleSaveIndustryMemo"
    />

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      :visible="showDeleteDialog"
      :content="deleteDialogContent"
      hint="删除后将无法恢复该备忘录"
      :is-loading="isDeleting"
      @confirm="confirmDeleteMemo"
      @cancel="closeDeleteDialog"
    />

    <!-- 编辑个人备忘录事件弹窗 -->
    <EventDirectEdit
      :show="showEditPersonMemoDialog"
      :event-data="personMemoEventData"
      @close="handleCloseEditPersonMemoDialog"
      @save="handleSaveEditPersonMemoDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import { showFailToast, showSuccessToast } from 'vant';
import Maintopbar from '@/components/Maintopbar.vue';
import inputBar from '@/components/Chat/inputBar.vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';
import PersonalMemo from '@/components/wenda/PersonalMemo.vue';
import CompanyKnowledgeBase from '@/components/wenda/CompanyKnowledgeBase.vue';
import AddPersonMemo from '@/components/wenda/AddPersonMemo.vue';
import AddIndustryMemo from '@/components/wenda/AddIndustryMemo.vue';
import EventDirectEdit from '@/components/Dialogs/EventDirectEdit.vue';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import { Typewriter } from '@/utils/typeWriter';
import { comprehensiveSearchStream } from '@/apis/search';
import { useUserStore } from '@/stores/user';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import { deletePersonEvent, type IEvent } from '@/apis/memory';

const router = useRouter();
const userStore = useUserStore();

// 输入框引用
const inputBarRef = ref<InstanceType<typeof inputBar> | null>(null);

// PersonalMemo 组件引用
const personalMemoRef = ref<InstanceType<typeof PersonalMemo> | null>(null);

// 录音状态
const isRecording = ref(false);

// 思考过程数据类型定义
type ThinkingData = {
  items: Array<{
    type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
    message?: string;
    step?: 'start' | 'processing' | 'complete';
    question?: string;
    questions?: string[];
    results?: Array<{ title: string; link: string }>;
  }>;
  isLoading: boolean;
};

// 定义扩展的消息类型，包含思考过程
type ExtendedChatMessage = IChatStreamContent | {
  role: 'thinking';
  key: string | number;
  thinkingData: ThinkingData;
};

// 聊天相关状态
const chatMessages = ref<ExtendedChatMessage[]>([]);
const currentUserId = ref('');
const currentPersonId = ref('');
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const isTypewriterStarted = ref(false);
const canSendMessage = ref(true);

// 当前正在处理的思考数据引用
const currentThinkingData = ref<ThinkingData | null>(null);

// 滚动容器引用
const scrollWrapper = ref<HTMLElement | null>(null);

// 弹窗状态
const showAddPersonMemo = ref(false);
const showAddIndustryMemo = ref(false);
const showEditPersonMemoDialog = ref(false);

// 编辑个人备忘录事件数据
const personMemoEventData = ref<IEvent | null>(null);

// 删除确认对话框状态
const showDeleteDialog = ref(false);
const isDeleting = ref(false);
const memoToDelete = ref<IEvent | null>(null);

// 删除对话框内容
const deleteDialogContent = computed(() => {
  return memoToDelete.value
    ? `确定要删除备忘录 "${memoToDelete.value.description_text}" 吗？`
    : '确定要删除该备忘录吗？';
});

// 创建打字机实例
const typewriter = new Typewriter(
  (str: string) => {
    if (str) {
      console.log('🖨️ [huida.vue] typewriter更新消息内容:', {
        displayLength: str.length,
        preview: str.substring(0, 50) + (str.length > 50 ? '...' : ''),
        messageIndex: chatMessages.value.length - 1,
        isStoppedByUser: isStoppedByUser.value,
      });

      // 添加安全检查，确保消息索引有效
      if (chatMessages.value.length > 0) {
        const lastMessage = chatMessages.value[chatMessages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = str;
          // 标记消息为完成状态
          lastMessage.isFinish = false; // 打字机过程中保持未完成状态
          // 移除这里的滚动调用，避免在打字机过程中影响用户滑动
        } else {
          console.warn('⚠️ [huida.vue] typewriter回调时最后一条消息不是助手消息');
        }
      } else {
        console.warn('⚠️ [huida.vue] typewriter回调时消息列表为空');
      }
    }
  },
  async () => {
    // 打字机自动完成回调
    console.log('✅ [huida.vue] typewriter自动完成回调触发');
    isTypewriterStarted.value = false;
    // 标记最后一条助手消息为完成状态
    if (chatMessages.value.length > 0) {
      const lastMessage = chatMessages.value[chatMessages.value.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.isFinish = true;
      }
    }
    // 打字机完成时，允许发送新消息
    canSendMessage.value = true;
    streamController.value = null;

    // 打字机完全完成后再滚动到底部
    await nextTick(() => {
      scrollToEnd();
    });
  },
);

// 滚动到底部
const scrollToEnd = () => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  }
};

// 处理表单提交
const handleFormSubmit = () => {
  // 阻止默认表单提交行为
};

// 处理输入发送
const handleInputSend = async (message: string) => {
  console.log('🔄 [huida.vue] 发送消息:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [huida.vue] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  await sendSearchMessage(message);
};

// 发送搜索消息
const sendSearchMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [huida.vue] 开始发送搜索消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [huida.vue] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;
  canSendMessage.value = false;

  // 创建新的思考数据实例
  const newThinkingData: ThinkingData = {
    items: [],
    isLoading: false,
  };

  // 设置当前思考数据引用
  currentThinkingData.value = newThinkingData;

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: messageContent,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 添加思考过程占位符 - 使用特殊的消息类型，包含独立的思考数据
  const thinkingMessage = {
    role: 'thinking' as const,
    key: Date.now() + 1,
    thinkingData: newThinkingData,
  };
  chatMessages.value.push(thinkingMessage);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  // 创建新的AbortController
  streamController.value = new AbortController();

  try {
    // 开始综合搜索
    await comprehensiveSearchStream(
      {
        question: messageContent,
        user_id: currentUserId.value,
      },
      {
        onStatus: (data) => {
          console.log('� [huida.vue] 状态更新:', data);
          if (currentThinkingData.value) {
            currentThinkingData.value.items.push({
              type: 'status',
              message: data.message,
              step: data.step,
            });
            if (data.step === 'start') {
              currentThinkingData.value.isLoading = true;
            } else if (data.step === 'complete') {
              currentThinkingData.value.isLoading = false;
            }
          }
        },
        onQuestion: (data) => {
          console.log('❓ [huida.vue] 问题:', data);
          if (currentThinkingData.value) {
            currentThinkingData.value.items.push({
              type: 'question',
              question: data.question,
            });
          }
        },
        onLog: (data) => {
          console.log('� [huida.vue] 日志:', data);
          if (currentThinkingData.value) {
            currentThinkingData.value.items.push({
              type: 'log',
              message: data.message,
            });
          }
        },
        onKnowledgeContext: (data) => {
          console.log('📚 [huida.vue] 知识上下文:', data);
          // 暂时不展示
        },
        onSearchQuestions: (data) => {
          console.log('� [huida.vue] 搜索问题:', data);
          if (currentThinkingData.value) {
            currentThinkingData.value.items.push({
              type: 'search_questions',
              questions: data.questions,
            });
          }
        },
        onSearchResult: (data) => {
          console.log('📋 [huida.vue] 搜索结果:', data);
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          if (currentThinkingData.value) {
            currentThinkingData.value.items.push({
              type: 'search_result',
              results,
            });
          }
        },
        onFinalAnswer: (data) => {
          console.log('✅ [huida.vue] 最终答案:', data);
          // 完成思考过程
          if (currentThinkingData.value) {
            currentThinkingData.value.isLoading = false;
          }

          // 构建完整答案
          const fullAnswer = `${data.core_answer}\n\n${data.detailed_answer}`;

          // 启动打字机（如果还未启动）
          if (!isTypewriterStarted.value) {
            console.log('🚀 [huida.vue] 启动typewriter，开始显示答案');
            isTypewriterStarted.value = true;
            typewriter.start();
          }

          // 将完整答案分解为单个字符，实现逐字显示效果
          const chunkSize = 1; // 每次显示1个字符，实现逐字效果
          let currentIndex = 0;
          
          const addChunk = () => {
            if (currentIndex < fullAnswer.length) {
              const nextIndex = Math.min(currentIndex + chunkSize, fullAnswer.length);
              const partialAnswer = fullAnswer.substring(0, nextIndex);
              typewriter.add(partialAnswer);
              currentIndex = nextIndex;
              
              // 继续添加下一个字符
              setTimeout(addChunk, 10); // 每50ms添加一个字符，更快的打字速度
            } else {
              // 所有字符都添加完毕，标记完成
              typewriter.markFinished();
            }
          };
          
          // 开始添加片段
          addChunk();
        },
        onError: (error) => {
          console.error('❌ [huida.vue] 搜索错误:', error);
          canSendMessage.value = true;
          streamController.value = null;
        },
        onClose: () => {
          console.log('🏁 [huida.vue] 搜索连接关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [huida.vue] 发送搜索消息失败:', error);
    canSendMessage.value = true;
    streamController.value = null;
  }
};

// 处理停止
const handleStop = () => {
  console.log('🔄 [huida.vue] 停止操作');
  if (streamController.value) {
    isStoppedByUser.value = true;
    streamController.value.abort();
    streamController.value = null;
    typewriter.stop();
    canSendMessage.value = true;
  }
};

// 处理录音状态变化
const handleRecordingStatus = (status: boolean) => {
  isRecording.value = status;
  console.log('🔄 [huida.vue] 录音状态变化:', status);
};

// 清理会话（开始新对话）
const clearChatSession = () => {
  console.log('🔄 [huida.vue] 清理会话，开始新对话');

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;

  // 清空消息列表和思考数据
  chatMessages.value = [];
  currentThinkingData.value = null;
  canSendMessage.value = true;
};

// 处理Home按钮点击 - 返回主页
const handleHome = async () => {
  console.log('🔄 [huida.vue] Home按钮点击，返回主页');
  await router.push({ name: 'chat' });
};

// 处理返回首页按钮点击
const handleBackToIndex = async () => {
  console.log('🔄 [huida.vue] 返回首页按钮点击');
  await router.push({ name: 'chat' });
};

// 处理添加个人备忘录
const handleAddPersonMemo = () => {
  console.log('🔄 [huida.vue] 打开添加个人备忘录弹窗');
  if (!currentPersonId.value) {
    console.warn('⚠️ [huida.vue] currentPersonId未加载，无法添加备忘录');
    showFailToast('用户信息未加载完成，请稍后再试');
    return;
  }
  showAddPersonMemo.value = true;
};

// 处理关闭个人备忘录弹窗
const handleCloseAddPersonMemo = () => {
  console.log('🔄 [huida.vue] 关闭添加个人备忘录弹窗');
  showAddPersonMemo.value = false;
};

// 处理保存个人备忘录
const handleSavePersonMemo = (content: string) => {
  console.log('🔄 [huida.vue] 保存个人备忘录:', content);
  showAddPersonMemo.value = false;

  // 刷新个人备忘录列表
  if (personalMemoRef.value) {
    console.log('🔄 [huida.vue] 刷新个人备忘录列表');
    personalMemoRef.value.fetchMemories();
  }
};

// 处理添加行业备忘录
const handleAddIndustryMemo = () => {
  console.log('🔄 [huida.vue] 打开添加行业备忘录弹窗');
  showAddIndustryMemo.value = true;
};

// 处理关闭行业备忘录弹窗
const handleCloseAddIndustryMemo = () => {
  console.log('🔄 [huida.vue] 关闭添加行业备忘录弹窗');
  showAddIndustryMemo.value = false;
};

// 处理保存行业备忘录
const handleSaveIndustryMemo = (content: string) => {
  console.log('🔄 [huida.vue] 保存行业备忘录:', content);
  showAddIndustryMemo.value = false;
  // 这里可以添加刷新行业备忘录列表的逻辑
};



// 处理编辑个人备忘录
const handleEditPersonMemo = (event: IEvent) => {
  console.log('🔄 [huida.vue] 编辑个人备忘录事件:', event);
  personMemoEventData.value = event;
  showEditPersonMemoDialog.value = true;
};

// 处理关闭编辑个人备忘录弹窗
const handleCloseEditPersonMemoDialog = () => {
  console.log('🔄 [huida.vue] 关闭编辑个人备忘录弹窗');
  showEditPersonMemoDialog.value = false;
  personMemoEventData.value = null;
};

// 处理保存编辑个人备忘录
const handleSaveEditPersonMemoDialog = (updatedEvent: IEvent) => {
  console.log('✅ [huida.vue] 个人备忘录事件编辑成功:', updatedEvent);
  showEditPersonMemoDialog.value = false;
  personMemoEventData.value = null;

  // 延迟1100ms后刷新PersonalMemo组件数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [huida.vue] 延迟刷新PersonalMemo数据（编辑事件成功）');
    if (personalMemoRef.value) {
      personalMemoRef.value.fetchMemories();
    }
  }, 1100);
};

// 处理删除备忘录
const handleDeleteMemo = (memory: IEvent) => {
  console.log('🔄 [huida.vue] 准备删除备忘录:', memory);
  memoToDelete.value = memory;
  showDeleteDialog.value = true;
};

// 确认删除备忘录
const confirmDeleteMemo = async () => {
  if (!memoToDelete.value) return;

  console.log('🔄 [huida.vue] 开始删除备忘录...', {
    userId: currentUserId.value,
    eventId: memoToDelete.value.event_id,
  });

  isDeleting.value = true;

  try {
    const response = await deletePersonEvent({
      user_id: currentUserId.value,
      event_id: memoToDelete.value.event_id,
    });

    if (response.result === 'success') {
      // 继续维持删除中状态1秒钟
      setTimeout(() => {
        showSuccessToast('删除成功');
        closeDeleteDialog();

        // 刷新个人备忘录列表
        if (personalMemoRef.value) {
          console.log('🔄 [huida.vue] 刷新个人备忘录列表');
          personalMemoRef.value.fetchMemories();
        }
      }, 1000);
    } else {
      showFailToast('删除失败');
      isDeleting.value = false;
    }
  } catch (error) {
    console.error('❌ [huida.vue] 删除备忘录失败:', error);
    showFailToast('删除失败');
    isDeleting.value = false;
  }
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  isDeleting.value = false;
  memoToDelete.value = null;
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [huida.vue] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [huida.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      userStore.userInfo = userInfo;
      console.log('✅ [huida.vue] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [huida.vue] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [huida.vue] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};



// 页面挂载时的初始化
onMounted(async () => {
  console.log('🔄 [huida.vue] 页面挂载完成');

  // 设置功能标记
  sessionStorage.setItem('chatFeature', 'question');

  // 加载用户信息（只调用一次）
  await loadUserInfo();

  // 获取用户的完整 person_id
  if (currentUserId.value && currentUserId.value !== 'unknown_user') {
    try {
      const userProfile = await getUserProfile({
        user_id: currentUserId.value,
      });

      if (userProfile && userProfile.result === 'success' && userProfile.person) {
        currentPersonId.value = userProfile.person.person_id;
        console.log('👤 [huida.vue] 用户 person_id 获取成功:', currentPersonId.value);
      } else {
        console.warn('⚠️ [huida.vue] 获取用户 person_id 失败:', userProfile);
        currentPersonId.value = currentUserId.value; // 降级使用 user_id
      }
    } catch (profileError) {
      console.error('❌ [huida.vue] 获取用户档案失败:', profileError);
      currentPersonId.value = currentUserId.value; // 降级使用 user_id
    }
  }

  // 初始化完成
  console.log('✅ [huida.vue] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔄 [huida.vue] 组件已卸载');

  // 如果有正在进行的请求，取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();

  // 清理完成
  console.log('✅ [huida.vue] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.huida-container {
  // 背景现在由主题系统控制
  height: 100vh; // 使用视口高度
  overflow: hidden;
  display: flex; // 添加flex布局
  flex-direction: column; // 纵向排列
}

.huida-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative; // 为弹窗提供定位基准
}

.reminder-content-container {
  gap: 10px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.huida-content-container {
  flex: 1;
  padding: 0px 0px 12px 0px;
  box-sizing: border-box;
  overflow: hidden;

  .chat-content {
    width: 100%;
    height: 100%;

    .chat-messages {
      width: 100%;
      height: 100%;
      padding: 32px 32px 80px 32px; // 增加底部padding，为预设问题和输入框留出充足空间，避免遮挡聊天内容
      overflow-y: auto;
      box-sizing: border-box;



      .thinking-process-container {
        margin: 16px 0;
        width: 100%;

        .thinking-process {
          width: 100%;
          background: var(--bg-glass-light);
          border: 2px solid var(--border-accent);
          border-radius: 16px;
          padding: 20px;
          backdrop-filter: blur(20px);
          box-shadow: var(--shadow-strong), var(--shadow-accent);

          .thinking-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-accent);

            .thinking-icon {
              font-size: 28px;
            }

            .thinking-title {
              font-size: 30px; // 增加字体大小到至少26px以上
              font-weight: 600;
              color: var(--text-primary);
            }
          }

          .thinking-content {
            .thinking-item {
              display: flex;
              align-items: flex-start;
              gap: 12px;
              margin-bottom: 16px;
              opacity: 1;
              transform: translateY(0);
              transition: all 0.3s ease;

              &.fade-in {
                opacity: 1;
                transform: translateY(0);
              }

              .thinking-item-icon {
                flex-shrink: 0;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;

                .status-dot {
                  width: 14px;
                  height: 14px;
                  border-radius: 50%;

                  &.start {
                    background: var(--primary-color);
                    animation: pulse 1.5s infinite;
                  }

                  &.processing {
                    background: var(--accent-color);
                    animation: pulse 1.5s infinite;
                  }

                  &.complete {
                    background: var(--success-color);
                  }
                }

                .question-icon,
                .log-icon,
                .search-icon,
                .result-icon {
                  font-size: 20px;
                }
              }

              .thinking-item-content {
                flex: 1;
                font-size: 26px; // 增加字体大小到至少26px
                line-height: 1.5;
                color: var(--text-secondary);

                .status-message {
                  color: var(--text-primary);
                  font-weight: 500;
                }

                .question-content {
                  .question-label {
                    color: var(--primary-color);
                    font-weight: 600;
                  }

                  .question-text {
                    color: var(--text-primary);
                  }
                }

                .log-content {
                  color: var(--text-secondary);
                }

                .search-questions-content {
                  .search-questions-label {
                    color: var(--primary-color);
                    font-weight: 600;
                    margin-bottom: 8px;
                  }

                  .questions-list {
                    margin: 0;
                    padding-left: 20px;

                    .question-item {
                      margin-bottom: 4px;
                      color: var(--text-secondary);
                    }
                  }
                }

                .search-result-content {
                  .search-result-title {
                    color: var(--primary-color);
                    font-weight: 600;
                    margin-bottom: 8px;
                  }

                  .result-item {
                    margin-bottom: 4px;

                    .result-link {
                      color: var(--text-secondary);
                      text-decoration: underline; // 默认显示下划线
                      font-style: italic; // 添加斜体样式

                      &:hover {
                        color: var(--primary-color);
                        text-decoration: underline;
                      }
                    }
                  }
                }
              }
            }
          }

          .loading-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;

            .loading-dots {
              display: flex;
              gap: 4px;

              .dot {
                display: inline-block;
                opacity: 0.3;
                animation: dotPulse 0.8s infinite;
                color: var(--primary-color);
                font-size: 24px;
                font-weight: bold;
              }

              .dot1 {
                animation-delay: 0s;
              }

              .dot2 {
                animation-delay: 0.25s;
              }

              .dot3 {
                animation-delay: 0.5s;
              }
            }

            .loading-text {
              color: var(--text-secondary);
              font-size: 26px; // 增加字体大小到至少26px
            }
          }
        }
      }
    }
  }
}

.footer {
  flex-shrink: 0;
  position: relative;
  z-index: 100;

  .input-wrapper {
    width: 100%;
    padding: 0px;
    background: transparent;
  }
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 0px 20px;
  display: flex;
  justify-content: flex-start;
  // 透明背景，与inputBar保持一致
  background: transparent;
  border: 2px solid var(--border-accent); // 添加左右边框
  border-top: none; // 去掉上边框
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  // 移除模糊效果

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 8px; // 减少间距，从12px改为8px
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 45px; // 缩小头像，从80px改为54px（约缩小1/3）
      height: 45px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: #000000; /* 改为黑色 */
        font-size: 28px; // 继续增大字体
        font-weight: 400;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #000000; /* 改为黑色 */
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 个人备忘录加载状态样式
.personal-memo-loading {
  height: 80px;
  width: 100%;
  background: #fff9c4; // 浅黄色底
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .memo-title {
    color: #f39c12; // 黄色标题
    font-size: 26px;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    overflow: hidden;

    .empty-memo {
      color: #d4a574;
      font-size: 24px;
      font-style: italic;
      white-space: nowrap;
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 思考过程动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
